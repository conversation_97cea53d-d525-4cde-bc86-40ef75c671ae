{
  // The name of the Zed theme to use for the UI.
  //
  // `mode` is one of:
  // - "system": Use the theme that corresponds to the system's appearance
  // - "light": Use the theme indicated by the "light" field
  // - "dark": Use the theme indicated by the "dark" field
  "theme": {
    "mode": "system",
    "light": "One Light",
    "dark": "One Dark"
  },
  "icon_theme": "Z<PERSON> (De<PERSON>ult)",
  // The name of a base set of key bindings to use.
  // This setting can take six values, each named after another
  // text editor:
  //
  // 1. "VSCode"
  // 2. "Atom"
  // 3. "JetBrains"
  // 4. "None"
  // 5. "SublimeText"
  // 6. "TextMate"
  "base_keymap": "VSCode",
  // Features that can be globally enabled or disabled
  "features": {
    // Which edit prediction provider to use.
    "edit_prediction_provider": "zed"
  },
  // The name of a font to use for rendering text in the editor
  "buffer_font_family": "Zed Plex Mono",
  // Set the buffer text's font fallbacks, this will be merged with
  // the platform's default fallbacks.
  "buffer_font_fallbacks": null,
  // The OpenType features to enable for text in the editor.
  "buffer_font_features": {
    // Disable ligatures:
    // "calt": false
  },
  // The default font size for text in the editor
  "buffer_font_size": 15,
  // The weight of the editor font in standard CSS units from 100 to 900.
  "buffer_font_weight": 400,
  // Set the buffer's line height.
  // May take 3 values:
  //  1. Use a line height that's comfortable for reading (1.618)
  //         "buffer_line_height": "comfortable"
  //  2. Use a standard line height, (1.3)
  //         "buffer_line_height": "standard",
  //  3. Use a custom line height
  //         "buffer_line_height": {
  //           "custom": 2
  //         },
  "buffer_line_height": "comfortable",
  // The name of a font to use for rendering text in the UI
  // You can set this to ".SystemUIFont" to use the system font
  "ui_font_family": "Zed Plex Sans",
  // Set the UI's font fallbacks, this will be merged with the platform's
  // default font fallbacks.
  "ui_font_fallbacks": null,
  // The OpenType features to enable for text in the UI
  "ui_font_features": {
    // Disable ligatures:
    "calt": false
  },
  // The weight of the UI font in standard CSS units from 100 to 900.
  "ui_font_weight": 400,
  // The default font size for text in the UI
  "ui_font_size": 16,
  // The default font size for text in the agent panel
  "agent_font_size": 16,
  // How much to fade out unused code.
  "unnecessary_code_fade": 0.3,
  // Active pane styling settings.
  "active_pane_modifiers": {
    // Inset border size of the active pane, in pixels.
    "border_size": 0.0,
    // Opacity of the inactive panes. 0 means transparent, 1 means opaque.
    // Values are clamped to the [0.0, 1.0] range.
    "inactive_opacity": 1.0
  },
  // Layout mode of the bottom dock. Defaults to "contained"
  "bottom_dock_layout": "contained",
  // The direction that you want to split panes horizontally. Defaults to "up"
  "pane_split_direction_horizontal": "up",
  // The direction that you want to split panes horizontally. Defaults to "left"
  "pane_split_direction_vertical": "left",
  // Centered layout related settings.
  "centered_layout": {
    // The relative width of the left padding of the central pane from the
    // workspace when the centered layout is used.
    "left_padding": 0.2,
    // The relative width of the right padding of the central pane from the
    // workspace when the centered layout is used.
    "right_padding": 0.2
  },
  // All settings related to the image viewer.
  "image_viewer": {
    // The unit for image file sizes.
    // By default we're setting it to binary.
    // The second option is decimal.
    "unit": "binary"
  },
  // Determines the modifier to be used to add multiple cursors with the mouse. The open hover link mouse gestures will adapt such that it do not conflict with the multicursor modifier.
  //
  // 1. Maps to `Alt` on Linux and Windows and to `Option` on MacOS:
  //    "alt"
  // 2. Maps `Control` on Linux and Windows and to `Command` on MacOS:
  //    "cmd_or_ctrl" (alias: "cmd", "ctrl")
  "multi_cursor_modifier": "alt",
  // Whether to enable vim modes and key bindings.
  "vim_mode": false,
  // Whether to show the informational hover box when moving the mouse
  // over symbols in the editor.
  "hover_popover_enabled": true,
  // Time to wait in milliseconds before showing the informational hover box.
  "hover_popover_delay": 300,
  // Whether to confirm before quitting Zed.
  "confirm_quit": false,
  // Whether to restore last closed project when fresh Zed instance is opened.
  "restore_on_startup": "last_session",
  // Whether to attempt to restore previous file's state when opening it again.
  // The state is stored per pane.
  // When disabled, defaults are applied instead of the state restoration.
  //
  // E.g. for editors, selections, folds and scroll positions are restored, if the same file is closed and, later, opened again in the same pane.
  // When disabled, a single selection in the very beginning of the file, zero scroll position and no folds state is used as a default.
  //
  // Default: true
  "restore_on_file_reopen": true,
  // Whether to automatically close files that have been deleted on disk.
  "close_on_file_delete": false,
  // Size of the drop target in the editor.
  "drop_target_size": 0.2,
  // Whether the window should be closed when using 'close active item' on a window with no tabs.
  // May take 3 values:
  //  1. Use the current platform's convention
  //         "when_closing_with_no_tabs": "platform_default"
  //  2. Always close the window:
  //         "when_closing_with_no_tabs": "close_window",
  //  3. Never close the window
  //         "when_closing_with_no_tabs": "keep_window_open",
  "when_closing_with_no_tabs": "platform_default",
  // What to do when the last window is closed.
  // May take 2 values:
  //  1. Use the current platform's convention
  //         "on_last_window_closed": "platform_default"
  //  2. Always quit the application
  //         "on_last_window_closed": "quit_app",
  "on_last_window_closed": "platform_default",
  // Whether to use the system provided dialogs for Open and Save As.
  // When set to false, Zed will use the built-in keyboard-first pickers.
  "use_system_path_prompts": true,
  // Whether to use the system provided dialogs for prompts, such as confirmation
  // prompts.
  // When set to false, Zed will use its built-in prompts. Note that on Linux,
  // this option is ignored and Zed will always use the built-in prompts.
  "use_system_prompts": true,
  // Whether the cursor blinks in the editor.
  "cursor_blink": true,
  // Cursor shape for the default editor.
  //  1. A vertical bar
  //     "bar"
  //  2. A block that surrounds the following character
  //     "block"
  //  3. An underline / underscore that runs along the following character
  //     "underline"
  //  4. A box drawn around the following character
  //     "hollow"
  //
  // Default: not set, defaults to "bar"
  "cursor_shape": null,
  // Determines when the mouse cursor should be hidden in an editor or input box.
  //
  // 1. Never hide the mouse cursor:
  //    "never"
  // 2. Hide only when typing:
  //    "on_typing"
  // 3. Hide on both typing and cursor movement:
  //    "on_typing_and_movement"
  "hide_mouse": "on_typing_and_movement",
  // Determines how snippets are sorted relative to other completion items.
  //
  // 1. Place snippets at the top of the completion list:
  //    "top"
  // 2. Place snippets normally without any preference:
  //    "inline"
  // 3. Place snippets at the bottom of the completion list:
  //    "bottom"
  "snippet_sort_order": "inline",
  // How to highlight the current line in the editor.
  //
  // 1. Don't highlight the current line:
  //    "none"
  // 2. Highlight the gutter area:
  //    "gutter"
  // 3. Highlight the editor area:
  //    "line"
  // 4. Highlight the full line (default):
  //    "all"
  "current_line_highlight": "all",
  // Whether to highlight all occurrences of the selected text in an editor.
  "selection_highlight": true,
  // The debounce delay before querying highlights from the language
  // server based on the current cursor location.
  "lsp_highlight_debounce": 75,
  // Whether to pop the completions menu while typing in an editor without
  // explicitly requesting it.
  "show_completions_on_input": true,
  // Whether to display inline and alongside documentation for items in the
  // completions menu
  "show_completion_documentation": true,
  // Show method signatures in the editor, when inside parentheses.
  "auto_signature_help": false,
  // Whether to show the signature help after completion or a bracket pair inserted.
  // If `auto_signature_help` is enabled, this setting will be treated as enabled also.
  "show_signature_help_after_edits": false,
  // Whether to show code action button at start of buffer line.
  "inline_code_actions": true,
  // Whether to allow drag and drop text selection in buffer.
  "drag_and_drop_selection": true,
  // What to do when go to definition yields no results.
  //
  // 1. Do nothing: `none`
  // 2. Find references for the same symbol: `find_all_references` (default)
  "go_to_definition_fallback": "find_all_references",
  // Which level to use to filter out diagnostics displayed in the editor.
  //
  // Affects the editor rendering only, and does not interrupt
  // the functionality of diagnostics fetching and project diagnostics editor.
  // Which files containing diagnostic errors/warnings to mark in the tabs.
  // Diagnostics are only shown when file icons are also active.
  // This setting only works when can take the following three values:
  //
  // Which diagnostic indicators to show in the scrollbar, their level should be more or equal to the specified severity level.
  // Possible values:
  //  - "off" — no diagnostics are allowed
  //  - "error"
  //  - "warning"
  //  - "info"
  //  - "hint"
  //  - null — allow all diagnostics (default)
  "diagnostics_max_severity": null,
  // Whether to show wrap guides (vertical rulers) in the editor.
  // Setting this to true will show a guide at the 'preferred_line_length' value
  // if 'soft_wrap' is set to 'preferred_line_length', and will show any
  // additional guides as specified by the 'wrap_guides' setting.
  "show_wrap_guides": true,
  // Character counts at which to show wrap guides in the editor.
  "wrap_guides": [],
  // Hide the values of in variables from visual display in private files
  "redact_private_values": false,
  // The default number of lines to expand excerpts in the multibuffer by.
  "expand_excerpt_lines": 5,
  // Globs to match against file paths to determine if a file is private.
  "private_files": ["**/.env*", "**/*.pem", "**/*.key", "**/*.cert", "**/*.crt", "**/secrets.yml"],
  // Whether to use additional LSP queries to format (and amend) the code after
  // every "trigger" symbol input, defined by LSP server capabilities.
  "use_on_type_format": true,
  // Whether to automatically add matching closing characters when typing
  // opening parenthesis, bracket, brace, single or double quote characters.
  // For example, when you type (, Zed will add a closing ) at the correct position.
  "use_autoclose": true,
  // Whether to automatically surround selected text when typing opening parenthesis,
  // bracket, brace, single or double quote characters.
  // For example, when you select text and type (, Zed will surround the text with ().
  "use_auto_surround": true,
  // Whether indentation of pasted content should be adjusted based on the context.
  "auto_indent_on_paste": true,
  // Controls how the editor handles the autoclosed characters.
  // When set to `false`(default), skipping over and auto-removing of the closing characters
  // happen only for auto-inserted characters.
  // Otherwise(when `true`), the closing characters are always skipped over and auto-removed
  // no matter how they were inserted.
  "always_treat_brackets_as_autoclosed": false,
  // Controls where the `editor::Rewrap` action is allowed in the current language scope.
  //
  // This setting can take three values:
  //
  // 1. Only allow rewrapping in comments:
  //    "in_comments"
  // 2. Only allow rewrapping in the current selection(s):
  //    "in_selections"
  // 3. Allow rewrapping anywhere:
  //    "anywhere"
  //
  // When using values other than `in_comments`, it is possible for the rewrapping to produce code
  // that is syntactically invalid. Keep this in mind when selecting which behavior you would like
  // to use.
  //
  // Note: This setting has no effect in Vim mode, as rewrap is already allowed everywhere.
  "allow_rewrap": "in_comments",
  // Controls whether edit predictions are shown immediately (true)
  // or manually by triggering `editor::ShowEditPrediction` (false).
  "show_edit_predictions": true,
  // Controls whether edit predictions are shown in a given language scope.
  // Example: ["string", "comment"]
  "edit_predictions_disabled_in": [],
  // Whether to show tabs and spaces in the editor.
  // This setting can take four values:
  //
  // 1. Draw tabs and spaces only for the selected text (default):
  //    "selection"
  // 2. Do not draw any tabs or spaces:
  //    "none"
  // 3. Draw all invisible symbols:
  //    "all"
  // 4. Draw whitespaces at boundaries only:
  //    "boundary"
  // 5. Draw whitespaces only after non-whitespace characters:
  //    "trailing"
  // For a whitespace to be on a boundary, any of the following conditions need to be met:
  // - It is a tab
  // - It is adjacent to an edge (start or end)
  // - It is adjacent to a whitespace (left or right)
  "show_whitespaces": "selection",
  // Settings related to calls in Zed
  "calls": {
    // Join calls with the microphone live by default
    "mute_on_join": false,
    // Share your project when you are the first to join a channel
    "share_on_join": false
  },
  // Toolbar related settings
  "toolbar": {
    // Whether to show breadcrumbs.
    "breadcrumbs": true,
    // Whether to show quick action buttons.
    "quick_actions": true,
    // Whether to show the Selections menu in the editor toolbar.
    "selections_menu": true,
    // Whether to show agent review buttons in the editor toolbar.
    "agent_review": true,
    // Whether to show code action buttons in the editor toolbar.
    "code_actions": false
  },
  // Titlebar related settings
  "title_bar": {
    // Whether to show the branch icon beside branch switcher in the titlebar.
    "show_branch_icon": false,
    // Whether to show the branch name button in the titlebar.
    "show_branch_name": true,
    // Whether to show the project host and name in the titlebar.
    "show_project_items": true,
    // Whether to show onboarding banners in the titlebar.
    "show_onboarding_banner": true,
    // Whether to show user picture in the titlebar.
    "show_user_picture": true,
    // Whether to show the sign in button in the titlebar.
    "show_sign_in": true
  },
  // Scrollbar related settings
  "scrollbar": {
    // When to show the scrollbar in the editor.
    // This setting can take four values:
    //
    // 1. Show the scrollbar if there's important information or
    //    follow the system's configured behavior (default):
    //   "auto"
    // 2. Match the system's configured behavior:
    //    "system"
    // 3. Always show the scrollbar:
    //    "always"
    // 4. Never show the scrollbar:
    //    "never"
    "show": "auto",
    // Whether to show cursor positions in the scrollbar.
    "cursors": true,
    // Whether to show git diff indicators in the scrollbar.
    "git_diff": true,
    // Whether to show buffer search results in the scrollbar.
    "search_results": true,
    // Whether to show selected text occurrences in the scrollbar.
    "selected_text": true,
    // Whether to show selected symbol occurrences in the scrollbar.
    "selected_symbol": true,
    // Which diagnostic indicators to show in the scrollbar:
    //  - "none" or false: do not show diagnostics
    //  - "error": show only errors
    //  - "warning": show only errors and warnings
    //  - "information": show only errors, warnings, and information
    //  - "all" or true: show all diagnostics
    "diagnostics": "all",
    // Forcefully enable or disable the scrollbar for each axis
    "axes": {
      // When false, forcefully disables the horizontal scrollbar. Otherwise, obey other settings.
      "horizontal": true,
      // When false, forcefully disables the vertical scrollbar. Otherwise, obey other settings.
      "vertical": true
    }
  },
  // Minimap related settings
  "minimap": {
    // When to show the minimap in the editor.
    // This setting can take three values:
    // 1. Show the minimap if the editor's scrollbar is visible:
    //    "auto"
    // 2. Always show the minimap:
    //    "always"
    // 3. Never show the minimap:
    //    "never" (default)
    "show": "never",
    // When to show the minimap thumb.
    // This setting can take two values:
    // 1. Show the minimap thumb if the mouse is over the minimap:
    //    "hover"
    // 2. Always show the minimap thumb:
    //    "always" (default)
    "thumb": "always",
    // How the minimap thumb border should look.
    // This setting can take five values:
    // 1. Display a border on all sides of the thumb:
    //    "thumb_border": "full"
    // 2. Display a border on all sides except the left side of the thumb:
    //    "thumb_border": "left_open" (default)
    // 3. Display a border on all sides except the right side of the thumb:
    //    "thumb_border": "right_open"
    // 4. Display a border only on the left side of the thumb:
    //    "thumb_border": "left_only"
    // 5. Display the thumb without any border:
    //    "thumb_border": "none"
    "thumb_border": "left_open",
    // How to highlight the current line in the minimap.
    // This setting can take the following values:
    //
    // 1. `null` to inherit the editor `current_line_highlight` setting (default)
    // 2. "line" or "all" to highlight the current line in the minimap.
    // 3. "gutter" or "none" to not highlight the current line in the minimap.
    "current_line_highlight": null
  },
  // Enable middle-click paste on Linux.
  "middle_click_paste": true,
  // What to do when multibuffer is double clicked in some of its excerpts
  // (parts of singleton buffers).
  // May take 2 values:
  //  1. Behave as a regular buffer and select the whole word (default).
  //         "double_click_in_multibuffer": "select"
  //  2. Open the excerpt clicked as a new buffer in the new tab.
  //         "double_click_in_multibuffer": "open",
  // For the case of "open", regular selection behavior can be achieved by holding `alt` when double clicking.
  "double_click_in_multibuffer": "select",
  "gutter": {
    // Whether to show line numbers in the gutter.
    "line_numbers": true,
    // Whether to show runnables buttons in the gutter.
    "runnables": true,
    // Whether to show breakpoints in the gutter.
    "breakpoints": true,
    // Whether to show fold buttons in the gutter.
    "folds": true,
    // Minimum number of characters to reserve space for in the gutter.
    "min_line_number_digits": 4
  },
  "indent_guides": {
    // Whether to show indent guides in the editor.
    "enabled": true,
    // The width of the indent guides in pixels, between 1 and 10.
    "line_width": 1,
    // The width of the active indent guide in pixels, between 1 and 10.
    "active_line_width": 1,
    // Determines how indent guides are colored.
    // This setting can take the following three values:
    //
    // 1. "disabled"
    // 2. "fixed"
    // 3. "indent_aware"
    "coloring": "fixed",
    // Determines how indent guide backgrounds are colored.
    // This setting can take the following two values:
    //
    // 1. "disabled"
    // 2. "indent_aware"
    "background_coloring": "disabled"
  },
  // Whether the editor will scroll beyond the last line.
  "scroll_beyond_last_line": "one_page",
  // The number of lines to keep above/below the cursor when scrolling.
  "vertical_scroll_margin": 3,
  // Whether to scroll when clicking near the edge of the visible text area.
  "autoscroll_on_clicks": false,
  // The number of characters to keep on either side when scrolling with the mouse
  "horizontal_scroll_margin": 5,
  // Scroll sensitivity multiplier. This multiplier is applied
  // to both the horizontal and vertical delta values while scrolling.
  "scroll_sensitivity": 1.0,
  // Scroll sensitivity multiplier for fast scrolling. This multiplier is applied
  // to both the horizontal and vertical delta values while scrolling. Fast scrolling
  // happens when a user holds the alt or option key while scrolling.
  "fast_scroll_sensitivity": 4.0,
  "relative_line_numbers": false,
  // If 'search_wrap' is disabled, search result do not wrap around the end of the file.
  "search_wrap": true,
  // Search options to enable by default when opening new project and buffer searches.
  "search": {
    // Whether to show the project search button in the status bar.
    "button": true,
    "whole_word": false,
    "case_sensitive": false,
    "include_ignored": false,
    "regex": false
  },
  // When to populate a new search's query based on the text under the cursor.
  // This setting can take the following three values:
  //
  // 1. Always populate the search query with the word under the cursor (default).
  //    "always"
  // 2. Only populate the search query when there is text selected
  //    "selection"
  // 3. Never populate the search query
  //    "never"
  "seed_search_query_from_cursor": "always",
  // When enabled, automatically adjusts search case sensitivity based on your query.
  // If your search query contains any uppercase letters, the search becomes case-sensitive;
  // if it contains only lowercase letters, the search becomes case-insensitive.
  "use_smartcase_search": false,
  // Inlay hint related settings
  "inlay_hints": {
    // Global switch to toggle hints on and off, switched off by default.
    "enabled": false,
    // Toggle certain types of hints on and off, all switched on by default.
    "show_type_hints": true,
    "show_parameter_hints": true,
    // Corresponds to null/None LSP hint type value.
    "show_other_hints": true,
    // Whether to show a background for inlay hints.
    //
    // If set to `true`, the background will use the `hint.background` color from the current theme.
    "show_background": false,
    // Time to wait after editing the buffer, before requesting the hints,
    // set to 0 to disable debouncing.
    "edit_debounce_ms": 700,
    // Time to wait after scrolling the buffer, before requesting the hints,
    // set to 0 to disable debouncing.
    "scroll_debounce_ms": 50,
    // A set of modifiers which, when pressed, will toggle the visibility of inlay hints.
    // If the set if empty or not all the modifiers specified are pressed, inlay hints will not be toggled.
    "toggle_on_modifiers_press": {
      "control": false,
      "shift": false,
      "alt": false,
      "platform": false,
      "function": false
    }
  },
  // Whether to resize all the panels in a dock when resizing the dock.
  // Can be a combination of "left", "right" and "bottom".
  "resize_all_panels_in_dock": ["left"],
  "project_panel": {
    // Whether to show the project panel button in the status bar
    "button": true,
    // Whether to hide the gitignore entries in the project panel.
    "hide_gitignore": false,
    // Default width of the project panel.
    "default_width": 240,
    // Where to dock the project panel. Can be 'left' or 'right'.
    "dock": "left",
    // Spacing between worktree entries in the project panel. Can be 'comfortable' or 'standard'.
    "entry_spacing": "comfortable",
    // Whether to show file icons in the project panel.
    "file_icons": true,
    // Whether to show folder icons or chevrons for directories in the project panel.
    "folder_icons": true,
    // Whether to show the git status in the project panel.
    "git_status": true,
    // Amount of indentation for nested items.
    "indent_size": 20,
    // Whether to reveal it in the project panel automatically,
    // when a corresponding project entry becomes active.
    // Gitignored entries are never auto revealed.
    "auto_reveal_entries": true,
    // Whether to fold directories automatically and show compact folders
    // (e.g. "a/b/c" ) when a directory has only one subdirectory inside.
    "auto_fold_dirs": true,
    // Scrollbar-related settings
    "scrollbar": {
      // When to show the scrollbar in the project panel.
      // This setting can take five values:
      //
      // 1. null (default): Inherit editor settings
      // 2. Show the scrollbar if there's important information or
      //    follow the system's configured behavior (default):
      //   "auto"
      // 3. Match the system's configured behavior:
      //    "system"
      // 4. Always show the scrollbar:
      //    "always"
      // 5. Never show the scrollbar:
      //    "never"
      "show": null
    },
    // Which files containing diagnostic errors/warnings to mark in the project panel.
    // This setting can take the following three values:
    //
    // 1. Do not mark any files:
    //    "off"
    // 2. Only mark files with errors:
    //    "errors"
    // 3. Mark files with errors and warnings:
    //    "all"
    "show_diagnostics": "all",
    // Settings related to indent guides in the project panel.
    "indent_guides": {
      // When to show indent guides in the project panel.
      // This setting can take two values:
      //
      // 1. Always show indent guides:
      //    "always"
      // 2. Never show indent guides:
      //    "never"
      "show": "always"
    },
    // Whether to hide the root entry when only one folder is open in the window.
    "hide_root": false
  },
  "outline_panel": {
    // Whether to show the outline panel button in the status bar
    "button": true,
    // Default width of the outline panel.
    "default_width": 300,
    // Where to dock the outline panel. Can be 'left' or 'right'.
    "dock": "left",
    // Whether to show file icons in the outline panel.
    "file_icons": true,
    // Whether to show folder icons or chevrons for directories in the outline panel.
    "folder_icons": true,
    // Whether to show the git status in the outline panel.
    "git_status": true,
    // Amount of indentation for nested items.
    "indent_size": 20,
    // Whether to reveal it in the outline panel automatically,
    // when a corresponding outline entry becomes active.
    // Gitignored entries are never auto revealed.
    "auto_reveal_entries": true,
    // Whether to fold directories automatically
    // when a directory has only one directory inside.
    "auto_fold_dirs": true,
    // Settings related to indent guides in the outline panel.
    "indent_guides": {
      // When to show indent guides in the outline panel.
      // This setting can take two values:
      //
      // 1. Always show indent guides:
      //    "always"
      // 2. Never show indent guides:
      //    "never"
      "show": "always"
    },
    // Scrollbar-related settings
    "scrollbar": {
      // When to show the scrollbar in the project panel.
      // This setting can take five values:
      //
      // 1. null (default): Inherit editor settings
      // 2. Show the scrollbar if there's important information or
      //    follow the system's configured behavior (default):
      //   "auto"
      // 3. Match the system's configured behavior:
      //    "system"
      // 4. Always show the scrollbar:
      //    "always"
      // 5. Never show the scrollbar:
      //    "never"
      "show": null
    }
  },
  "collaboration_panel": {
    // Whether to show the collaboration panel button in the status bar.
    "button": true,
    // Where to dock the collaboration panel. Can be 'left' or 'right'.
    "dock": "left",
    // Default width of the collaboration panel.
    "default_width": 240
  },
  "chat_panel": {
    // When to show the chat panel button in the status bar.
    // Can be 'never', 'always', or 'when_in_call',
    // or a boolean (interpreted as 'never'/'always').
    "button": "when_in_call",
    // Where to the chat panel. Can be 'left' or 'right'.
    "dock": "right",
    // Default width of the chat panel.
    "default_width": 240
  },
  "git_panel": {
    // Whether to show the git panel button in the status bar.
    "button": true,
    // Where to show the git panel. Can be 'left' or 'right'.
    "dock": "left",
    // Default width of the git panel.
    "default_width": 360,
    // Style of the git status indicator in the panel.
    //
    // Default: icon
    "status_style": "icon",
    // What branch name to use if init.defaultBranch
    // is not set
    //
    // Default: main
    "fallback_branch_name": "main",
    // Whether to sort entries in the panel by path
    // or by status (the default).
    //
    // Default: false
    "sort_by_path": false,
    "scrollbar": {
      // When to show the scrollbar in the git panel.
      //
      // Default: inherits editor scrollbar settings
      "show": null
    }
  },
  "message_editor": {
    // Whether to automatically replace emoji shortcodes with emoji characters.
    // For example: typing `:wave:` gets replaced with `👋`.
    "auto_replace_emoji_shortcode": true
  },
  "notification_panel": {
    // Whether to show the notification panel button in the status bar.
    "button": true,
    // Where to dock the notification panel. Can be 'left' or 'right'.
    "dock": "right",
    // Default width of the notification panel.
    "default_width": 380
  },
  "agent": {
    // Version of this setting.
    "version": "2",
    // Whether the agent is enabled.
    "enabled": true,
    /// What completion mode to start new threads in, if available. Can be 'normal' or 'burn'.
    "preferred_completion_mode": "normal",
    // Whether to show the agent panel button in the status bar.
    "button": true,
    // Where to dock the agent panel. Can be 'left', 'right' or 'bottom'.
    "dock": "right",
    // Default width when the agent panel is docked to the left or right.
    "default_width": 640,
    // Default height when the agent panel is docked to the bottom.
    "default_height": 320,
    // The default model to use when creating new threads.
    "default_model": {
      // The provider to use.
      "provider": "zed.dev",
      // The model to use.
      "model": "claude-sonnet-4"
    },
    // Additional parameters for language model requests. When making a request to a model, parameters will be taken
    // from the last entry in this list that matches the model's provider and name. In each entry, both provider
    // and model are optional, so that you can specify parameters for either one.
    "model_parameters": [
      // To set parameters for all requests to OpenAI models:
      // {
      //   "provider": "openai",
      //   "temperature": 0.5
      // }
      //
      // To set parameters for all requests in general:
      // {
      //   "temperature": 0
      // }
      //
      // To set parameters for a specific provider and model:
      // {
      //   "provider": "zed.dev",
      //   "model": "claude-sonnet-4",
      //   "temperature": 1.0
      // }
    ],
    // When enabled, the agent can run potentially destructive actions without asking for your confirmation.
    "always_allow_tool_actions": false,
    // When enabled, the agent will stream edits.
    "stream_edits": false,
    // When enabled, agent edits will be displayed in single-file editors for review
    "single_file_review": true,
    // When enabled, show voting thumbs for feedback on agent edits.
    "enable_feedback": true,
    "default_profile": "write",
    "profiles": {
      "write": {
        "name": "Write",
        "enable_all_context_servers": true,
        "tools": {
          "copy_path": true,
          "create_directory": true,
          "delete_path": true,
          "diagnostics": true,
          "edit_file": true,
          "fetch": true,
          "list_directory": true,
          "move_path": true,
          "now": true,
          "find_path": true,
          "read_file": true,
          "grep": true,
          "terminal": true,
          "thinking": true,
          "web_search": true
        }
      },
      "ask": {
        "name": "Ask",
        // We don't know which of the context server tools are safe for the "Ask" profile, so we don't enable them by default.
        // "enable_all_context_servers": true,
        "tools": {
          "contents": true,
          "diagnostics": true,
          "fetch": true,
          "list_directory": true,
          "now": true,
          "find_path": true,
          "read_file": true,
          "open": true,
          "grep": true,
          "thinking": true,
          "web_search": true
        }
      },
      "minimal": {
        "name": "Minimal",
        "enable_all_context_servers": false,
        "tools": {}
      }
    },
    // Where to show notifications when the agent has either completed
    // its response, or else needs confirmation before it can run a
    // tool action.
    // "primary_screen" - Show the notification only on your primary screen (default)
    // "all_screens" - Show these notifications on all screens
    // "never" - Never show these notifications
    "notify_when_agent_waiting": "primary_screen",
    // Whether to play a sound when the agent has either completed
    // its response, or needs user input.

    // Default: false
    "play_sound_when_agent_done": false
  },
  // The settings for slash commands.
  "slash_commands": {
    // Settings for the `/docs` slash command.
    "docs": {
      // Whether `/docs` is enabled.
      "enabled": false
    },
    // Settings for the `/project` slash command.
    "project": {
      // Whether `/project` is enabled.
      "enabled": false
    }
  },
  // Whether the screen sharing icon is shown in the os status bar.
  "show_call_status_icon": true,
  // Whether to use language servers to provide code intelligence.
  "enable_language_server": true,
  // Whether to perform linked edits of associated ranges, if the language server supports it.
  // For example, when editing opening <html> tag, the contents of the closing </html> tag will be edited as well.
  "linked_edits": true,
  // The list of language servers to use (or disable) for all languages.
  //
  // This is typically customized on a per-language basis.
  "language_servers": ["..."],
  // When to automatically save edited buffers. This setting can
  // take four values.
  //
  // 1. Never automatically save:
  //     "autosave": "off",
  // 2. Save when changing focus away from the Zed window:
  //     "autosave": "on_window_change",
  // 3. Save when changing focus away from a specific buffer:
  //     "autosave": "on_focus_change",
  // 4. Save when idle for a certain amount of time:
  //     "autosave": { "after_delay": {"milliseconds": 500} },
  "autosave": "off",
  // Maximum number of tabs per pane. Unset for unlimited.
  "max_tabs": null,
  // Settings related to the editor's tab bar.
  "tab_bar": {
    // Whether or not to show the tab bar in the editor
    "show": true,
    // Whether or not to show the navigation history buttons.
    "show_nav_history_buttons": true,
    // Whether or not to show the tab bar buttons.
    "show_tab_bar_buttons": true
  },
  // Settings related to the editor's tabs
  "tabs": {
    // Show git status colors in the editor tabs.
    "git_status": false,
    // Position of the close button on the editor tabs.
    // One of: ["right", "left", "hidden"]
    "close_position": "right",
    // Whether to show the file icon for a tab.
    "file_icons": false,
    // Controls the appearance behavior of the tab's close button.
    //
    // 1. Show it just upon hovering the tab. (default)
    //     "hover"
    // 2. Show it persistently.
    //     "always"
    // 3. Never show it, even if hovering it.
    //     "hidden"
    "show_close_button": "hover",
    // What to do after closing the current tab.
    //
    // 1. Activate the tab that was open previously (default)
    //     "history"
    // 2. Activate the right neighbour tab if present
    //     "neighbour"
    // 3. Activate the left neighbour tab if present
    //     "left_neighbour"
    "activate_on_close": "history",
    // Which files containing diagnostic errors/warnings to mark in the tabs.
    // Diagnostics are only shown when file icons are also active.
    // This setting only works when can take the following three values:
    //
    // 1. Do not mark any files:
    //    "off"
    // 2. Only mark files with errors:
    //    "errors"
    // 3. Mark files with errors and warnings:
    //    "all"
    "show_diagnostics": "off"
  },
  // Settings related to preview tabs.
  "preview_tabs": {
    // Whether preview tabs should be enabled.
    // Preview tabs allow you to open files in preview mode, where they close automatically
    // when you switch to another file unless you explicitly pin them.
    // This is useful for quickly viewing files without cluttering your workspace.
    "enabled": true,
    // Whether to open tabs in preview mode when selected from the file finder.
    "enable_preview_from_file_finder": false,
    // Whether a preview tab gets replaced when code navigation is used to navigate away from the tab.
    "enable_preview_from_code_navigation": false
  },
  // Settings related to the file finder.
  "file_finder": {
    // Whether to show file icons in the file finder.
    "file_icons": true,
    // Determines how much space the file finder can take up in relation to the available window width.
    // There are 5 possible width values:
    //
    // 1. Small: This value is essentially a fixed width.
    //    "modal_max_width": "small"
    // 2. Medium:
    //    "modal_max_width": "medium"
    // 3. Large:
    //    "modal_max_width": "large"
    // 4. Extra Large:
    //    "modal_max_width": "xlarge"
    // 5. Fullscreen: This value removes any horizontal padding, as it consumes the whole viewport width.
    //    "modal_max_width": "full"
    //
    // Default: small
    "modal_max_width": "small",
    // Determines whether the file finder should skip focus for the active file in search results.
    // There are 2 possible values:
    //
    // 1. true: When searching for files, if the currently active file appears as the first result,
    //    auto-focus will skip it and focus the second result instead.
    //    "skip_focus_for_active_in_search": true
    //
    // 2. false: When searching for files, the first result will always receive focus,
    //    even if it's the currently active file.
    //    "skip_focus_for_active_in_search": false
    //
    // Default: true
    "skip_focus_for_active_in_search": true,
    // Whether to show the git status in the file finder.
    "git_status": true,
    // Whether to use gitignored files when searching.
    // Only the file Zed had indexed will be used, not necessary all the gitignored files.
    //
    // Can accept 3 values:
    //   * `true`: Use all gitignored files
    //   * `false`: Use only the files Zed had indexed
    //   * `null`: Be smart and search for ignored when called from a gitignored worktree
    "include_ignored": null
  },
  // Whether or not to remove any trailing whitespace from lines of a buffer
  // before saving it.
  "remove_trailing_whitespace_on_save": true,
  // Whether to start a new line with a comment when a previous line is a comment as well.
  "extend_comment_on_newline": true,
  // Removes any lines containing only whitespace at the end of the file and
  // ensures just one newline at the end.
  "ensure_final_newline_on_save": true,
  // Whether or not to perform a buffer format before saving
  //
  // Keep in mind, if the autosave with delay is enabled, format_on_save will be ignored
  "format_on_save": "on",
  // How to perform a buffer format. This setting can take 4 values:
  //
  // 1. Format code using the current language server:
  //     "formatter": "language_server"
  // 2. Format code using an external command:
  //     "formatter": {
  //       "external": {
  //         "command": "prettier",
  //         "arguments": ["--stdin-filepath", "{buffer_path}"]
  //       }
  //     }
  // 3. Format code using Zed's Prettier integration:
  //     "formatter": "prettier"
  // 4. Default. Format files using Zed's Prettier integration (if applicable),
  //    or falling back to formatting via language server:
  //     "formatter": "auto"
  "formatter": "auto",
  // How to soft-wrap long lines of text.
  // Possible values:
  //
  // 1. Prefer a single line generally, unless an overly long line is encountered.
  //      "soft_wrap": "none",
  //      "soft_wrap": "prefer_line", // (deprecated, same as "none")
  // 2. Soft wrap lines that overflow the editor.
  //      "soft_wrap": "editor_width",
  // 3. Soft wrap lines at the preferred line length.
  //      "soft_wrap": "preferred_line_length",
  // 4. Soft wrap lines at the preferred line length or the editor width (whichever is smaller).
  //      "soft_wrap": "bounded",
  "soft_wrap": "none",
  // The column at which to soft-wrap lines, for buffers where soft-wrap
  // is enabled.
  "preferred_line_length": 80,
  // Whether to indent lines using tab characters, as opposed to multiple
  // spaces.
  "hard_tabs": false,
  // How many columns a tab should occupy.
  "tab_size": 4,
  // What debuggers are preferred by default for all languages.
  "debuggers": [],
  // Control what info is collected by Zed.
  "telemetry": {
    // Send debug info like crash reports.
    "diagnostics": true,
    // Send anonymized usage data like what languages you're using Zed with.
    "metrics": true
  },
  // Automatically update Zed. This setting may be ignored on Linux if
  // installed through a package manager.
  "auto_update": true,
  // Diagnostics configuration.
  "diagnostics": {
    // Whether to show the project diagnostics button in the status bar.
    "button": true,
    // Whether to show warnings or not by default.
    "include_warnings": true,
    // Settings for using LSP pull diagnostics mechanism in Zed.
    "lsp_pull_diagnostics": {
      // Whether to pull for diagnostics or not.
      "enabled": true,
      // Minimum time to wait before pulling diagnostics from the language server(s).
      // 0 turns the debounce off.
      "debounce_ms": 50
    },
    // Settings for inline diagnostics
    "inline": {
      // Whether to show diagnostics inline or not
      "enabled": false,
      // The delay in milliseconds to show inline diagnostics after the
      // last diagnostic update.
      "update_debounce_ms": 150,
      // The amount of padding between the end of the source line and the start
      // of the inline diagnostic in units of em widths.
      "padding": 4,
      // The minimum column to display inline diagnostics. This setting can be
      // used to horizontally align inline diagnostics at some column. Lines
      // longer than this value will still push diagnostics further to the right.
      "min_column": 0,
      // The minimum severity of the diagnostics to show inline.
      // Inherits editor's diagnostics' max severity settings when `null`.
      "max_severity": null
    },
    "cargo": {
      // When enabled, Zed disables rust-analyzer's check on save and starts to query
      // Cargo diagnostics separately.
      "fetch_cargo_diagnostics": false
    }
  },
  // Files or globs of files that will be excluded by Zed entirely. They will be skipped during file
  // scans, file searches, and not be displayed in the project file tree. Takes precedence over `file_scan_inclusions`.
  "file_scan_exclusions": [
    "**/.git",
    "**/.svn",
    "**/.hg",
    "**/.jj",
    "**/CVS",
    "**/.DS_Store",
    "**/Thumbs.db",
    "**/.classpath",
    "**/.settings"
  ],
  // Files or globs of files that will be included by Zed, even when ignored by git. This is useful
  // for files that are not tracked by git, but are still important to your project. Note that globs
  // that are overly broad can slow down Zed's file scanning. `file_scan_exclusions` takes
  // precedence over these inclusions.
  "file_scan_inclusions": [".env*"],
  // Git gutter behavior configuration.
  "git": {
    // Control whether the git gutter is shown. May take 2 values:
    // 1. Show the gutter
    //      "git_gutter": "tracked_files"
    // 2. Hide the gutter
    //      "git_gutter": "hide"
    "git_gutter": "tracked_files",
    // Control whether the git blame information is shown inline,
    // in the currently focused line.
    "inline_blame": {
      "enabled": true
      // Sets a delay after which the inline blame information is shown.
      // Delay is restarted with every cursor movement.
      // "delay_ms": 600
      //
      // Whether or not to display the git commit summary on the same line.
      // "show_commit_summary": false
      //
      // The minimum column number to show the inline blame information at
      // "min_column": 0
    },
    // How git hunks are displayed visually in the editor.
    // This setting can take two values:
    //
    // 1. Show unstaged hunks filled and staged hunks hollow:
    //    "hunk_style": "staged_hollow"
    // 2. Show unstaged hunks hollow and staged hunks filled:
    //    "hunk_style": "unstaged_hollow"
    "hunk_style": "staged_hollow"
  },
  // The list of custom Git hosting providers.
  "git_hosting_providers": [
    // {
    //   "provider": "github",
    //   "name": "BigCorp GitHub",
    //   "base_url": "https://code.big-corp.com"
    // }
  ],
  // Configuration for how direnv configuration should be loaded. May take 2 values:
  // 1. Load direnv configuration using `direnv export json` directly.
  //      "load_direnv": "direct"
  // 2. Load direnv configuration through the shell hook, works for POSIX shells and fish.
  //      "load_direnv": "shell_hook"
  "load_direnv": "direct",
  "edit_predictions": {
    // A list of globs representing files that edit predictions should be disabled for.
    // There's a sensible default list of globs already included.
    // Any addition to this list will be merged with the default list.
    // Globs are matched relative to the worktree root,
    // except when starting with a slash (/) or equivalent in Windows.
    "disabled_globs": ["**/.env*", "**/*.pem", "**/*.key", "**/*.cert", "**/*.crt", "**/.dev.vars", "**/secrets.yml"],
    // When to show edit predictions previews in buffer.
    // This setting takes two possible values:
    // 1. Display predictions inline when there are no language server completions available.
    //     "mode": "eager"
    // 2. Display predictions inline only when holding a modifier key (alt by default).
    //     "mode": "subtle"
    "mode": "eager",
    // Whether edit predictions are enabled when editing text threads.
    // This setting has no effect if globally disabled.
    "enabled_in_text_threads": true
  },
  // Settings specific to journaling
  "journal": {
    // The path of the directory where journal entries are stored
    "path": "~",
    // What format to display the hours in
    // May take 2 values:
    // 1. hour12
    // 2. hour24
    "hour_format": "hour12"
  },
  // Settings specific to the terminal
  "terminal": {
    // What shell to use when opening a terminal. May take 3 values:
    // 1. Use the system's default terminal configuration in /etc/passwd
    //      "shell": "system"
    // 2. A program:
    //      "shell": {
    //        "program": "sh"
    //      }
    // 3. A program with arguments:
    //     "shell": {
    //         "with_arguments": {
    //           "program": "/bin/bash",
    //           "args": ["--login"]
    //         }
    //     }
    "shell": "system",
    // Where to dock terminals panel. Can be `left`, `right`, `bottom`.
    "dock": "bottom",
    // Default width when the terminal is docked to the left or right.
    "default_width": 640,
    // Default height when the terminal is docked to the bottom.
    "default_height": 320,
    // What working directory to use when launching the terminal.
    // May take 4 values:
    // 1. Use the current file's project directory.  Will Fallback to the
    //    first project directory strategy if unsuccessful
    //      "working_directory": "current_project_directory"
    // 2. Use the first project in this workspace's directory
    //      "working_directory": "first_project_directory"
    // 3. Always use this platform's home directory (if we can find it)
    //     "working_directory": "always_home"
    // 4. Always use a specific directory. This value will be shell expanded.
    //    If this path is not a valid directory the terminal will default to
    //    this platform's home directory  (if we can find it)
    //      "working_directory": {
    //        "always": {
    //          "directory": "~/zed/projects/"
    //        }
    //      }
    "working_directory": "current_project_directory",
    // Set the cursor blinking behavior in the terminal.
    // May take 3 values:
    //  1. Never blink the cursor, ignoring the terminal mode
    //         "blinking": "off",
    //  2. Default the cursor blink to off, but allow the terminal to
    //     set blinking
    //         "blinking": "terminal_controlled",
    //  3. Always blink the cursor, ignoring the terminal mode
    //         "blinking": "on",
    "blinking": "terminal_controlled",
    // Default cursor shape for the terminal.
    //  1. A block that surrounds the following character
    //     "block"
    //  2. A vertical bar
    //     "bar"
    //  3. An underline / underscore that runs along the following character
    //     "underline"
    //  4. A box drawn around the following character
    //     "hollow"
    //
    // Default: not set, defaults to "block"
    "cursor_shape": null,
    // Set whether Alternate Scroll mode (code: ?1007) is active by default.
    // Alternate Scroll mode converts mouse scroll events into up / down key
    // presses when in the alternate screen (e.g. when running applications
    // like vim or  less). The terminal can still set and unset this mode.
    // May take 2 values:
    //  1. Default alternate scroll mode to on
    //         "alternate_scroll": "on",
    //  2. Default alternate scroll mode to off
    //         "alternate_scroll": "off",
    "alternate_scroll": "on",
    // Set whether the option key behaves as the meta key.
    // May take 2 values:
    //  1. Rely on default platform handling of option key, on macOS
    //     this means generating certain unicode characters
    //         "option_as_meta": false,
    //  2. Make the option keys behave as a 'meta' key, e.g. for emacs
    //         "option_as_meta": true,
    "option_as_meta": false,
    // Whether or not selecting text in the terminal will automatically
    // copy to the system clipboard.
    "copy_on_select": false,
    // Whether to show the terminal button in the status bar
    "button": true,
    // Any key-value pairs added to this list will be added to the terminal's
    // environment. Use `:` to separate multiple values.
    "env": {
      // "KEY": "value1:value2"
    },
    // Set the terminal's line height.
    // May take 3 values:
    //  1. Use a line height that's comfortable for reading, 1.618
    //         "line_height": "comfortable"
    //  2. Use a standard line height, 1.3. This option is useful for TUIs,
    //      particularly if they use box characters
    //         "line_height": "standard",
    //  3. Use a custom line height.
    //         "line_height": {
    //           "custom": 2
    //         },
    "line_height": "comfortable",
    // Activate the python virtual environment, if one is found, in the
    // terminal's working directory (as resolved by the working_directory
    // setting). Set this to "off" to disable this behavior.
    "detect_venv": {
      "on": {
        // Default directories to search for virtual environments, relative
        // to the current working directory. We recommend overriding this
        // in your project's settings, rather than globally.
        "directories": [".env", "env", ".venv", "venv"],
        // Can also be `csh`, `fish`, `nushell` and `power_shell`
        "activate_script": "default"
      }
    },
    "toolbar": {
      // Whether to display the terminal title in its toolbar's breadcrumbs.
      // Only shown if the terminal title is not empty.
      //
      // The shell running in the terminal needs to be configured to emit the title.
      // Example: `echo -e "\e]2;New Title\007";`
      "breadcrumbs": true
    },
    // Scrollbar-related settings
    "scrollbar": {
      // When to show the scrollbar in the terminal.
      // This setting can take five values:
      //
      // 1. null (default): Inherit editor settings
      // 2. Show the scrollbar if there's important information or
      //    follow the system's configured behavior (default):
      //   "auto"
      // 3. Match the system's configured behavior:
      //    "system"
      // 4. Always show the scrollbar:
      //    "always"
      // 5. Never show the scrollbar:
      //    "never"
      "show": null
    }
    // Set the terminal's font size. If this option is not included,
    // the terminal will default to matching the buffer's font size.
    // "font_size": 15,
    // Set the terminal's font family. If this option is not included,
    // the terminal will default to matching the buffer's font family.
    // "font_family": "Zed Plex Mono",
    // Set the terminal's font fallbacks. If this option is not included,
    // the terminal will default to matching the buffer's font fallbacks.
    // This will be merged with the platform's default font fallbacks
    // "font_fallbacks": ["FiraCode Nerd Fonts"],
    // Sets the maximum number of lines in the terminal's scrollback buffer.
    // Default: 10_000, maximum: 100_000 (all bigger values set will be treated as 100_000), 0 disables the scrolling.
    // Existing terminals will not pick up this change until they are recreated.
    // "max_scroll_history_lines": 10000,
  },
  "code_actions_on_format": {},
  // Settings related to running tasks.
  "tasks": {
    "variables": {},
    "enabled": true,
    // Use LSP tasks over Zed language extension ones.
    // If no LSP tasks are returned due to error/timeout or regular execution,
    // Zed language extension tasks will be used instead.
    //
    // Other Zed tasks will still be shown:
    // * Zed task from either of the task config file
    // * Zed task from history (e.g. one-off task was spawned before)
    //
    // Default: true
    "prefer_lsp": true
  },
  // An object whose keys are language names, and whose values
  // are arrays of filenames or extensions of files that should
  // use those languages.
  //
  // For example, to treat files like `foo.notjs` as JavaScript,
  // and `Embargo.lock` as TOML:
  //
  // {
  //   "JavaScript": ["notjs"],
  //   "TOML": ["Embargo.lock"]
  // }
  //
  "file_types": {
    "JSONC": ["**/.zed/**/*.json", "**/zed/**/*.json", "**/Zed/**/*.json", "**/.vscode/**/*.json"],
    "Shell Script": [".env.*"]
  },
  // Settings for which version of Node.js and NPM to use when installing
  // language servers and Copilot.
  //
  // Note: changing this setting currently requires restarting Zed.
  "node": {
    // By default, Zed will look for `node` and `npm` on your `$PATH`, and use the
    // existing executables if their version is recent enough. Set this to `true`
    // to prevent this, and force Zed to always download and install its own
    // version of Node.
    "ignore_system_version": false,
    // You can also specify alternative paths to Node and NPM. If you specify
    // `path`, but not `npm_path`, Zed will assume that `npm` is located at
    // `${path}/../npm`.
    "path": null,
    "npm_path": null
  },
  // The extensions that Zed should automatically install on startup.
  //
  // If you don't want any of these extensions, add this field to your settings
  // and change the value to `false`.
  "auto_install_extensions": {
    "html": true
  },
  // Controls how completions are processed for this language.
  "completions": {
    // Controls how words are completed.
    // For large documents, not all words may be fetched for completion.
    //
    // May take 3 values:
    // 1. "enabled"
    //   Always fetch document's words for completions along with LSP completions.
    // 2. "fallback"
    //   Only if LSP response errors or times out, use document's words to show completions.
    // 3. "disabled"
    //   Never fetch or complete document's words for completions.
    //   (Word-based completions can still be queried via a separate action)
    //
    // Default: fallback
    "words": "fallback",
    // Whether to fetch LSP completions or not.
    //
    // Default: true
    "lsp": true,
    // When fetching LSP completions, determines how long to wait for a response of a particular server.
    // When set to 0, waits indefinitely.
    //
    // Default: 0
    "lsp_fetch_timeout_ms": 0,
    // Controls what range to replace when accepting LSP completions.
    //
    // When LSP servers give an `InsertReplaceEdit` completion, they provides two ranges: `insert` and `replace`. Usually, `insert`
    // contains the word prefix before your cursor and `replace` contains the whole word.
    //
    // Effectively, this setting just changes whether Zed will use the received range for `insert` or `replace`, so the results may
    // differ depending on the underlying LSP server.
    //
    // Possible values:
    // 1. "insert"
    //   Replaces text before the cursor, using the `insert` range described in the LSP specification.
    // 2. "replace"
    //   Replaces text before and after the cursor, using the `replace` range described in the LSP specification.
    // 3. "replace_subsequence"
    //   Behaves like `"replace"` if the text that would be replaced is a subsequence of the completion text,
    //   and like `"insert"` otherwise.
    // 4. "replace_suffix"
    //   Behaves like `"replace"` if the text after the cursor is a suffix of the completion, and like
    //   `"insert"` otherwise.
    "lsp_insert_mode": "replace_suffix"
  },
  // Different settings for specific languages.
  "languages": {
    "Astro": {
      "language_servers": ["astro-language-server", "..."],
      "prettier": {
        "allowed": true,
        "plugins": ["prettier-plugin-astro"]
      }
    },
    "Blade": {
      "prettier": {
        "allowed": true
      }
    },
    "C": {
      "format_on_save": "off",
      "use_on_type_format": false,
      "prettier": {
        "allowed": false
      }
    },
    "C++": {
      "format_on_save": "off",
      "use_on_type_format": false,
      "prettier": {
        "allowed": false
      }
    },
    "CSS": {
      "prettier": {
        "allowed": true
      }
    },
    "Dart": {
      "tab_size": 2
    },
    "Diff": {
      "show_edit_predictions": false,
      "remove_trailing_whitespace_on_save": false,
      "ensure_final_newline_on_save": false
    },
    "Elixir": {
      "language_servers": ["elixir-ls", "!next-ls", "!lexical", "..."]
    },
    "Elm": {
      "tab_size": 4
    },
    "Erlang": {
      "language_servers": ["erlang-ls", "!elp", "..."]
    },
    "Git Commit": {
      "allow_rewrap": "anywhere",
      "soft_wrap": "editor_width",
      "preferred_line_length": 72
    },
    "Go": {
      "code_actions_on_format": {
        "source.organizeImports": true
      },
      "debuggers": ["Delve"]
    },
    "GraphQL": {
      "prettier": {
        "allowed": true
      }
    },
    "HEEX": {
      "language_servers": ["elixir-ls", "!next-ls", "!lexical", "..."]
    },
    "HTML": {
      "prettier": {
        "allowed": true
      }
    },
    "Java": {
      "prettier": {
        "allowed": true,
        "plugins": ["prettier-plugin-java"]
      }
    },
    "JavaScript": {
      "language_servers": ["!typescript-language-server", "vtsls", "..."],
      "prettier": {
        "allowed": true
      }
    },
    "JSON": {
      "prettier": {
        "allowed": true
      }
    },
    "JSONC": {
      "prettier": {
        "allowed": true
      }
    },
    "LaTeX": {
      "formatter": "language_server",
      "language_servers": ["texlab", "..."],
      "prettier": {
        "allowed": true,
        "plugins": ["prettier-plugin-latex"]
      }
    },
    "Markdown": {
      "format_on_save": "off",
      "use_on_type_format": false,
      "allow_rewrap": "anywhere",
      "soft_wrap": "editor_width",
      "prettier": {
        "allowed": true
      }
    },
    "PHP": {
      "language_servers": ["phpactor", "!intelephense", "..."],
      "prettier": {
        "allowed": true,
        "plugins": ["@prettier/plugin-php"],
        "parser": "php"
      }
    },
    "Plain Text": {
      "allow_rewrap": "anywhere"
    },
    "Python": {
      "debuggers": ["Debugpy"]
    },
    "Ruby": {
      "language_servers": ["solargraph", "!ruby-lsp", "!rubocop", "!sorbet", "!steep", "..."]
    },
    "Rust": {
      "debuggers": ["CodeLLDB"]
    },
    "SCSS": {
      "prettier": {
        "allowed": true
      }
    },
    "Starlark": {
      "language_servers": ["starpls", "!buck2-lsp", "..."]
    },
    "Svelte": {
      "language_servers": ["svelte-language-server", "..."],
      "prettier": {
        "allowed": true,
        "plugins": ["prettier-plugin-svelte"]
      }
    },
    "TSX": {
      "language_servers": ["!typescript-language-server", "vtsls", "..."],
      "prettier": {
        "allowed": true
      }
    },
    "Twig": {
      "prettier": {
        "allowed": true
      }
    },
    "TypeScript": {
      "language_servers": ["!typescript-language-server", "vtsls", "..."],
      "prettier": {
        "allowed": true
      }
    },
    "Vue.js": {
      "language_servers": ["vue-language-server", "..."],
      "prettier": {
        "allowed": true
      }
    },
    "XML": {
      "prettier": {
        "allowed": true,
        "plugins": ["@prettier/plugin-xml"]
      }
    },
    "YAML": {
      "prettier": {
        "allowed": true
      }
    },
    "Zig": {
      "language_servers": ["zls", "..."]
    }
  },
  // Different settings for specific language models.
  "language_models": {
    "anthropic": {
      "version": "1",
      "api_url": "https://api.anthropic.com"
    },
    "google": {
      "api_url": "https://generativelanguage.googleapis.com"
    },
    "ollama": {
      "api_url": "http://localhost:11434"
    },
    "openai": {
      "version": "1",
      "api_url": "https://api.openai.com/v1"
    },
    "open_router": {
      "api_url": "https://openrouter.ai/api/v1"
    },
    "lmstudio": {
      "api_url": "http://localhost:1234/api/v0"
    },
    "deepseek": {
      "api_url": "https://api.deepseek.com"
    },
    "mistral": {
      "api_url": "https://api.mistral.ai/v1"
    }
  },
  // Zed's Prettier integration settings.
  // Allows to enable/disable formatting with Prettier
  // and configure default Prettier, used when no project-level Prettier installation is found.
  "prettier": {
    // // Whether to consider prettier formatter or not when attempting to format a file.
    // "allowed": false,
    //
    // // Use regular Prettier json configuration.
    // // If Prettier is allowed, Zed will use this for its Prettier instance for any applicable file, if
    // // the project has no other Prettier installed.
    // "plugins": [],
    //
    // // Use regular Prettier json configuration.
    // // If Prettier is allowed, Zed will use this for its Prettier instance for any applicable file, if
    // // the project has no other Prettier installed.
    // "trailingComma": "es5",
    // "tabWidth": 4,
    // "semi": false,
    // "singleQuote": true
  },
  // Settings for auto-closing of JSX tags.
  "jsx_tag_auto_close": {
    "enabled": true
  },
  // LSP Specific settings.
  "lsp": {
    // Specify the LSP name as a key here.
    // "rust-analyzer": {
    //     // A special flag for rust-analyzer integration, to use server-provided tasks
    //     enable_lsp_tasks": true,
    //     // These initialization options are merged into Zed's defaults
    //     "initialization_options": {
    //         "check": {
    //             "command": "clippy" // rust-analyzer.check.command (default: "check")
    //         }
    //     }
    // }
  },
  // Jupyter settings
  "jupyter": {
    "enabled": true
    // Specify the language name as the key and the kernel name as the value.
    // "kernel_selections": {
    //    "python": "conda-base"
    //    "typescript": "deno"
    // }
  },
  // Vim settings
  "vim": {
    "default_mode": "normal",
    "toggle_relative_line_numbers": false,
    "use_system_clipboard": "always",
    "use_multiline_find": false,
    "use_smartcase_find": false,
    "highlight_on_yank_duration": 200,
    "custom_digraphs": {},
    // Cursor shape for the each mode.
    // Specify the mode as the key and the shape as the value.
    // The mode can be one of the following: "normal", "replace", "insert", "visual".
    // The shape can be one of the following: "block", "bar", "underline", "hollow".
    "cursor_shape": {}
  },
  // The server to connect to. If the environment variable
  // ZED_SERVER_URL is set, it will override this setting.
  "server_url": "https://zed.dev",
  // Settings overrides to use when using Zed Preview.
  // Mostly useful for developers who are managing multiple instances of Zed.
  "preview": {
    // "theme": "Andromeda"
  },
  // Settings overrides to use when using Zed Nightly.
  // Mostly useful for developers who are managing multiple instances of Zed.
  "nightly": {
    // "theme": "Andromeda"
  },
  // Settings overrides to use when using Zed Stable.
  // Mostly useful for developers who are managing multiple instances of Zed.
  "stable": {
    // "theme": "Andromeda"
  },
  // Settings overrides to use when using Zed Dev.
  // Mostly useful for developers who are managing multiple instances of Zed.
  "dev": {
    // "theme": "Andromeda"
  },
  // Whether to show full labels in line indicator or short ones
  //
  // Values:
  //   - `short`: "2 s, 15 l, 32 c"
  //   - `long`: "2 selections, 15 lines, 32 characters"
  // Default: long
  "line_indicator_format": "long",
  // Set a proxy to use. The proxy protocol is specified by the URI scheme.
  //
  // Supported URI scheme: `http`, `https`, `socks4`, `socks4a`, `socks5`,
  // `socks5h`. `http` will be used when no scheme is specified.
  //
  // By default no proxy will be used, or Zed will try get proxy settings from
  // environment variables.
  //
  // Examples:
  //   - "proxy": "socks5h://localhost:10808"
  //   - "proxy": "http://127.0.0.1:10809"
  "proxy": null,
  // Set to configure aliases for the command palette.
  // When typing a query which is a key of this object, the value will be used instead.
  //
  // Examples:
  // {
  //   "W": "workspace::Save"
  // }
  "command_aliases": {},
  // ssh_connections is an array of ssh connections.
  // You can configure these from `project: Open Remote` in the command palette.
  // Zed's ssh support will pull configuration from your ~/.ssh too.
  // Examples:
  // [
  //   {
  //     "host": "example-box",
  //     // "port": 22, "username": "test", "args": ["-i", "/home/<USER>/.ssh/id_rsa"]
  //     "projects": [
  //       {
  //         "paths": ["/home/<USER>/code/zed"]
  //       }
  //     ]
  //   }
  // ]
  "ssh_connections": [],
  // Whether to read ~/.ssh/config for ssh connection sources.
  "read_ssh_config": true,
  // Configures context servers for use by the agent.
  "context_servers": {},
  "debugger": {
    "stepping_granularity": "line",
    "save_breakpoints": true,
    "button": true
  }
}
